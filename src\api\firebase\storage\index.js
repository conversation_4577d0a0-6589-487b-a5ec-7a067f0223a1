// get image from storage
import firebase from "firebase";
import * as url from "url";

// 取得可以下載的圖片連結(path + token)
// like: https://firebasestorage.googleapis.com/v0/b/hkbdb-web.appspot.com/o/image%2Fbook-library-with-open-textbook.jpg?alt=media&amp;token=5651d384-c34d-4621-82fe-e78d85d7b330
const getImage = url => {
    const storage = firebase.storage();
    const storageRef = storage.ref(url);
    return storageRef
        .getDownloadURL()
        .then(urlToken => {
            // Insert url into an <img> tag to "download"
            return Promise.resolve(urlToken);
        })
        .catch(error => {
            // A full list of error codes is available at
            // https://firebase.google.com/docs/storage/web/handle-errors
            switch (error.code) {
                case "storage/object-not-found":
                    // File doesn't exist
                    return Promise.reject(error);
                // break;
                case "storage/unauthorized":
                    // User doesn't have permission to access the object
                    return Promise.reject(error);
                // break;
                case "storage/canceled":
                    // User canceled the upload
                    return Promise.reject(error);
                // break;
                case "storage/unknown":
                    // Unknown error occurred, inspect the server response
                    return Promise.reject(error);
                // break;
            }
        });
};

const getSwgJson = url => {
    const storage = firebase.storage();
    const storageRef = storage.ref(url);
    return storageRef
        .getDownloadURL()
        .then(urlToken => {
            // Insert url into an <img> tag to "download"
            return Promise.resolve(urlToken);
        })
        .catch(error => {
            // A full list of error codes is available at
            // https://firebase.google.com/docs/storage/web/handle-errors
            switch (error.code) {
                case "storage/object-not-found":
                    // File doesn't exist
                    return Promise.reject(error);
                // break;
                case "storage/unauthorized":
                    // User doesn't have permission to access the object
                    return Promise.reject(error);
                // break;
                case "storage/canceled":
                    // User canceled the upload
                    return Promise.reject(error);
                // break;
                case "storage/unknown":
                    // Unknown error occurred, inspect the server response
                    return Promise.reject(error);
                // break;
            }
        });
};

export { getImage, getSwgJson };
