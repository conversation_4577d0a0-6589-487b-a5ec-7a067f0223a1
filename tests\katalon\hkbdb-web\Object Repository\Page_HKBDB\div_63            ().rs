<?xml version="1.0" encoding="UTF-8"?>
<WebElementEntity>
   <description></description>
   <name>div_63            ()</name>
   <tag></tag>
   <elementGuidId>00426179-3da2-42fb-95c1-9958b58e7e2d</elementGuidId>
   <imagePath></imagePath>
   <selectorCollection>
      <entry>
         <key>BASIC</key>
         <value>//*[(text() = '
            63
            (香港)
    ' or . = '
            63
            (香港)
    ')]</value>
      </entry>
      <entry>
         <key>CSS</key>
         <value>div.cluster-marker-icon.cluster-marker-icon-undefined.in-hong-kong.near-hong-kong</value>
      </entry>
      <entry>
         <key>IMAGE</key>
         <value></value>
      </entry>
      <entry>
         <key>XPATH</key>
         <value>//div[@id='root']/div/div/div/div[2]/div[2]/div[2]/div/div[4]/div/div/div/div[4]/div/div/div</value>
      </entry>
   </selectorCollection>
   <selectorMethod>CSS</selectorMethod>
   <smartLocatorCollection>
      <entry>
         <key>SMART_LOCATOR</key>
         <value>internal:role=button[name=&quot;63 (香港)&quot;i]</value>
      </entry>
   </smartLocatorCollection>
   <smartLocatorEnabled>false</smartLocatorEnabled>
   <useRalativeImagePath>true</useRalativeImagePath>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>tag</name>
      <type>Main</type>
      <value>div</value>
      <webElementGuid>395b897f-d91f-4eb8-af0e-92d37fd783ed</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>class</name>
      <type>Main</type>
      <value>cluster-marker-icon cluster-marker-icon-undefined in-hong-kong near-hong-kong</value>
      <webElementGuid>32d065f3-16ce-4039-8b7b-cc934667f5d6</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>text</name>
      <type>Main</type>
      <value>
            63
            (香港)
    </value>
      <webElementGuid>99a9fcd0-94b1-445f-b38e-a893a79a668b</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath</name>
      <type>Main</type>
      <value>id(&quot;root&quot;)/div[1]/div[1]/div[@class=&quot;pushable&quot;]/div[@class=&quot;pusher&quot;]/div[@class=&quot;gis-map-container MuiBox-root css-0&quot;]/div[@class=&quot;MuiBox-root css-0&quot;]/div[@class=&quot;MuiBox-root css-8atqhb&quot;]/div[@class=&quot;MuiBox-root css-i5qpif&quot;]/div[@class=&quot;MuiBox-root css-7szfa9&quot;]/div[@class=&quot;leaflet-container leaflet-touch leaflet-fade-anim leaflet-grab leaflet-touch-drag leaflet-touch-zoom&quot;]/div[@class=&quot;leaflet-pane leaflet-map-pane&quot;]/div[@class=&quot;leaflet-pane leaflet-marker-pane&quot;]/div[@class=&quot;leaflet-marker-icon leaflet-div-icon leaflet-zoom-animated leaflet-interactive&quot;]/div[@class=&quot;cluster-marker-icon-wrapper&quot;]/div[@class=&quot;cluster-marker-icon cluster-marker-icon-undefined in-hong-kong near-hong-kong&quot;]</value>
      <webElementGuid>8802d701-9662-40d0-959b-96f43db48899</webElementGuid>
   </webElementProperties>
   <webElementXpaths>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:idRelative</name>
      <type>Main</type>
      <value>//div[@id='root']/div/div/div/div[2]/div[2]/div[2]/div/div[4]/div/div/div/div[4]/div/div/div</value>
      <webElementGuid>d3709878-5ce5-4dee-8a07-7934fdcf577e</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='線條圖例'])[1]/following::div[24]</value>
      <webElementGuid>86db3268-ebd5-45c8-9ee2-d07c1dcbc011</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='使用說明'])[1]/following::div[101]</value>
      <webElementGuid>41b4e776-b8f7-4137-9033-894aec6a3e36</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='協恩中學,香港,香港市政局,香港政府市政局,香港中文大學藝術系、香港特別行政區政 ...'])[1]/preceding::div[110]</value>
      <webElementGuid>41a95bd3-dc21-4191-ac70-73b0460fea6e</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:position</name>
      <type>Main</type>
      <value>//div[4]/div/div/div/div[4]/div/div/div</value>
      <webElementGuid>f3ffcae9-5008-4fd8-9edf-1bbd18d4aa34</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:customAttributes</name>
      <type>Main</type>
      <value>//div[(text() = '
            63
            (香港)
    ' or . = '
            63
            (香港)
    ')]</value>
      <webElementGuid>0c4b9e0c-c15d-4416-b9ff-06d68a7c1548</webElementGuid>
   </webElementXpaths>
</WebElementEntity>
