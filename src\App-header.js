import React from "react";
// auth
import authority from "./App-authority";
import { FormattedMessage } from "react-intl";
// HTML Header: Nav Bar
// authority: 使用者是否可以進入該頁面由 authority 控制
// public: 開發中的頁面, public 設為 false
export const menus = {
    menuLeft: [
        {
            id: "menu-left-01",
            label: "Browse",
            path: "/:locale/browse",
            public: true,
            position: "left",
            labelIntl18: (
                <FormattedMessage
                    id={"menu.browse"}
                    defaultMessage={"Browse"}
                />
            ),
            intlFormatMessageId: "menu.browse",
            intlDefaultMessage: "Browse",
            authority: authority.Browse
        },
        {
            id: "menu-left-2",
            label: "GIS",
            path: "/:locale/map",
            public: true, // 維護中設定 false
            position: "left",
            labelIntl18: (
                <FormattedMessage id={"menu.map"} defaultMessage={"GIS"} />
            ),
            intlFormatMessageId: "menu.map",
            intlDefaultMessage: "GIS",
            authority: authority.Map
        },
        {
            id: "menu-left-3",
            label: "Query",
            path: "/:locale/query",
            public: true, // 維護中設定 false
            position: "left",
            labelIntl18: (
                <FormattedMessage id={"menu.query"} defaultMessage={"Query"} />
            ),
            intlFormatMessageId: "menu.query",
            intlDefaultMessage: "Query",
            authority: authority.Query
        },
        {
            id: "menu-left-4",
            label: "Sources",
            path: "/:locale/sources",
            public: true,
            position: "left",
            labelIntl18: (
                <FormattedMessage
                    id={"menu.sources"}
                    defaultMessage={"Sources"}
                />
            ),
            intlFormatMessageId: "menu.sources",
            intlDefaultMessage: "Sources",
            authority: authority.Sources
        },
        {
            id: "menu-left-5",
            label: "About",
            path: "/:locale/about",
            public: true, // 維護中設定 false
            position: "left",
            labelIntl18: (
                <FormattedMessage id={"menu.about"} defaultMessage={"About"} />
            ),
            intlFormatMessageId: "menu.about",
            intlDefaultMessage: "About",
            authority: authority.About
        },
        {
            id: "menu-left-6",
            label: "Ontology",
            path: "/:locale/ontology",
            public: true, // 維護中設定 false
            position: "left",
            labelIntl18: (
                <FormattedMessage
                    id={"menu.ontology"}
                    defaultMessage={"Ontology"}
                />
            ),
            intlFormatMessageId: "menu.ontology",
            intlDefaultMessage: "Ontology",
            authority: authority.Ontology
        },
        {
            id: "menu-left-7",
            label: "API",
            path: "/:locale/api",
            public: true,
            position: "left",
            labelIntl18: (
                <FormattedMessage id={"menu.api"} defaultMessage={"API"} />
            ),
            intlFormatMessageId: "menu.api",
            intlDefaultMessage: "API",
            authority: authority.API
        }
        // {
        //     id: "menu-left-8",
        //     label: "hkvayb",
        //     path: "/:locale/hkvayb",
        //     public: true,
        //     position: "left",
        //     labelIntl18: (
        //         <FormattedMessage id={"menu.hkvayb"} defaultMessage={"hkvayb"} />
        //     ),
        //     intlFormatMessageId: "menu.hkvayb",
        //     intlDefaultMessage: "hkvayb",
        //     authority: authority.hkvayb
        // }
    ],
    menuRight: [
        {
            id: "menu-right-1",
            label: "User",
            path: "/:locale/user",
            public: false, // 若在維護中請設定 false
            position: "right",
            icon: "user",
            labelIntl18: (
                <FormattedMessage id={"menu.user"} defaultMessage={"User"} />
            ),
            intlFormatMessageId: "menu.user",
            intlDefaultMessage: "User",
            authority: authority.User
        },
        {
            id: "menu-right-2",
            label: "Management system", // redirect to backend web
            path: "/:locale/management",
            public: true,
            position: "right",
            icon: "lock",
            labelIntl18: (
                <FormattedMessage
                    id={"menu.managementSystem"}
                    defaultMessage={"Management system"}
                />
            ),
            intlFormatMessageId: "menu.managementSystem",
            intlDefaultMessage: "Management system",
            authority: authority.Management
        },
        {
            id: "menu-right-3",
            label: "Log Out",
            path: "/:locale/logout",
            public: true,
            position: "right",
            icon: "sign out",
            labelIntl18: (
                <FormattedMessage
                    id={"menu.logout"}
                    defaultMessage={"Log Out"}
                />
            ),
            intlFormatMessageId: "menu.logout",
            intlDefaultMessage: "Log Out",
            authority: authority.Logout
        }
    ]
};

/**
 * @currentAuthority: String
 * @authority: Array
 * */
export const isPermitting = (currentAuthority, authority) => {
    return authority.includes(currentAuthority);
};
