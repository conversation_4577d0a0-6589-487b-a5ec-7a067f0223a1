import React, { useContext, useEffect, useState, useCallback } from "react";
import { Checkbox, FormControlLabel, FormGroup } from "@mui/material";
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import debounce from '../../utils/debounce'

const CheckboxList = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { mapFilterOptions } = state.map;
    const [localOptions, setLocalOptions] = useState(mapFilterOptions);

    // Debounced dispatch function
    const debouncedDispatch = useCallback(
        debounce((updatedOptions) => {
            dispatch({
                type: Act.SET_MAP_FILTER_OPTIONS,
                payload: updatedOptions
            });
        }, 500),
        [dispatch]
    );

    useEffect(() => {
        setLocalOptions(mapFilterOptions);
    }, [mapFilterOptions]);

    const handleChange = event => {
        const { name, checked } = event.target;
        const updatedOptions = {
            ...localOptions,
            [name]: { ...localOptions[name], status: checked }
        };
        setLocalOptions(updatedOptions);

        debouncedDispatch(updatedOptions);
    };

    return (
        <FormGroup>
            {Object.keys(localOptions).map(item => (
                <FormControlLabel
                    key={item}
                    control={
                        <Checkbox
                            checked={localOptions[item].status}
                            onChange={handleChange}
                            name={item}
                            sx={{
                                "&.Mui-checked": {
                                    color: "#104860"
                                }
                            }}
                        />
                    }
                    label={localOptions[item].label}
                    sx={{
                        span: {
                            fontSize: "12px"
                        }
                    }}
                />
            ))}
        </FormGroup>
    );
};

export default CheckboxList;
