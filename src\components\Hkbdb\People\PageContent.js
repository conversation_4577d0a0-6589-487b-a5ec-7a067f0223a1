import React, { useContext, useState, useEffect } from "react";

// ui
import { Button, Container, Segment, Tab } from "semantic-ui-react";

// custom
import { MainLoader } from "../EntityComponent/MainLoader";

// lang
import { injectIntl } from "react-intl";
import { intlMsgs } from "../EntityComponent/entityIntlMsgs";

// action
import {
    queryPersonInfo,
    queryPersonNameNode,
    queryTimeLine,
    queryGenealogy,
    queryInverseRelationInfo,
    queryOntologyOneOfThemInfo,
    queryPersonInfoV2
} from "./action";

// component
import Information from "./Information/Information";
import Timeline from "./Timeline/Timeline";
import SNA from "./SNA/SNA";
import Tree from "../EntityComponent/Tree/Tree";

import PropTypes from "prop-types";
import UnderConstruction from "../../../common/components/UnderConstruction";
import { createEmail } from "../../Auth/email.api";
// store
import { StoreContext } from "../../../store/StoreProvider";

// api
import {
    Api,
    deleteHkbdbData,
    doRestCreate,
    readHkbdbData
} from "../../../api/hkbdb/Api";
import { bs64Decode, isEmpty } from "../../../common/codes";
import config from "../../../config/config";
import emailConfig from "../../../config/config-email";
import Act from "../../../store/actions";
import { SPECIAL_HEADER } from "../Organization/action";
import WarningModal from "../EntityComponent/TableComp/SugComponents/SugWarningModal";
import SugSubmitHintModal from "../EntityComponent/TableComp/SugComponents/SugSubmitHintModal";

/**
 * @typedef {object} PeoplePageContentProps
 * @property {string} intl
 * @property {string} name 已經移除 prefix 的 instance id
 * @property {string} permission
 * @property {string} className
 */

/**
 *
 * @param props {PeoplePageContentProps}
 * @returns {JSX.Element}
 * @constructor
 */
const PeoplePageContent = props => {
    const { intl, name, permission, className } = props;

    const [state, dispatch] = useContext(StoreContext);
    const { renderSignal } = state.information;
    const { information, user, personInformation: perInfo } = state;
    const {
        memberInvert,
        tempUpdateSuggestInfo,
        entryGraphForCreatingSuggestions
    } = perInfo;

    const { personId, ontologyOneOfThemData } = information;
    // const { user } = state;
    const { locale, email, displayName } = user;

    const [isLoading, setIsLoading] = useState(false);
    const [isSaved, setIsSaved] = useState(false);
    const [isOpenHintModal, setIsOpenHintModal] = useState(false);
    const [isCreatingSuggestions, setIsCreatingSuggestions] = useState(false);
    const { formatMessage } = intl;

    const deleteOriginEvent = async (ogData, ontologyType, createGraphData) => {
        if (
            ontologyType === "person" ||
            ontologyType === "article" ||
            ontologyType === "publication" ||
            ontologyType === "otherwork" ||
            ontologyType === "relationevent"
        )
            return;

        if (isEmpty(ogData.data)) return;

        const subjectKey = "PER" + bs64Decode(createGraphData.srcId.slice(3));
        const eventKey = ogData.data.find(i => i.s === subjectKey)?.o;

        // eslint-disable-next-line no-undef
        const cloneData = structuredClone(createGraphData);
        cloneData.value = {};
        cloneData.srcId = eventKey.slice(0, 3) + btoa(eventKey.slice(3));

        await deleteHkbdbData(Api.deleteEvent(), cloneData);
    };

    const handleCreate = async () => {
        try {
            setIsCreatingSuggestions(true);
            const allResults = await Promise.all(
                Object.entries(tempUpdateSuggestInfo)
                    .flatMap(([key, value]) => {
                        // 如果 value 是空，跳過
                        if (isEmpty(value)) return [];
                        // 確保 value 是一個非空陣列，並對每個 item 進行處理
                        if (Array.isArray(value) && value.length > 0) {
                            return value.map(async item => {
                                // 檢查物件的所有屬性，看是否只有 id 和 graph 有值
                                const itemKeys = Object.keys(item);
                                const hasOnlyIdAndGraph = itemKeys.every(
                                    key =>
                                        key === "id" ||
                                        key === "graph" ||
                                        item[key] === "" ||
                                        item[key] === undefined ||
                                        item[key] === null
                                );

                                // 如果物件只有 id 和 graph，就跳過這個項目
                                if (hasOnlyIdAndGraph) {
                                    return;
                                }

                                // 過濾掉 item 中的空字串值以及 id 鍵
                                const processedValue = Object.fromEntries(
                                    Object.entries(item).filter(
                                        ([subKey, subVal]) =>
                                            subKey !== "id" &&
                                            subKey !== "graph" &&
                                            subVal !== ""
                                    )
                                );

                                // 不同表單及不同時間創造的suggestions需要有獨立的graph
                                const generateUniqueGraph = originalGraph => {
                                    // First replace any 13-digit number
                                    let newGraph = originalGraph.replace(
                                        /\d{13}/,
                                        Date.now().toString()
                                    );

                                    // Then replace the value between second and third slash
                                    const parts = newGraph.split("/");
                                    if (parts.length >= 4) {
                                        parts[2] = key;
                                    }
                                    newGraph = parts.join("/");

                                    return newGraph;
                                };
                                const tmpGraph = item["graph"];
                                const entry = {
                                    classType: key,
                                    srcId:
                                        entryGraphForCreatingSuggestions.srcId,
                                    value: processedValue,
                                    // graph: generateUniqueGraph(
                                    //     entryGraphForCreatingSuggestions.graph,
                                    //     key
                                    // )
                                    graph: tmpGraph
                                };
                                // setIsLoading(true);

                                if (!isEmpty(personId)) {
                                    // 特殊處理 "member" 鍵
                                    if (key === "member") {
                                        const name =
                                            entry.value?.[SPECIAL_HEADER];
                                        let memberType = entry.classType;
                                        const memberValue = Object.keys(
                                            entry.value
                                        ).reduce((acc, curKey) => {
                                            if (
                                                curKey === SPECIAL_HEADER ||
                                                isEmpty(entry.value[curKey])
                                            )
                                                return acc;
                                            if (memberInvert[curKey]) {
                                                memberType =
                                                    memberInvert[curKey]
                                                        .classtype;
                                                acc[
                                                    memberInvert[
                                                        curKey
                                                    ].property
                                                ] = name;
                                            }
                                            acc[curKey] = entry.value[curKey];
                                            return acc;
                                        }, {});
                                        entry.classType = memberType;
                                        entry.value = memberValue;
                                    }

                                    // 檢查 label，若無則補上空值
                                    const tmpValueKeyArr = Object.keys(
                                        entry?.value
                                    );
                                    if (
                                        tmpValueKeyArr.filter(
                                            tmpPropKey =>
                                                tmpPropKey.indexOf("label_") ===
                                                -1
                                        ) &&
                                        key !== "member"
                                    ) {
                                        entry.value = {
                                            [`label_${key}__string`]: "",
                                            ...entry.value // 以後面的值覆蓋前面的
                                        };
                                    }

                                    // 多語系處理
                                    Object.keys(entry.value).forEach(valKey => {
                                        if (
                                            config.MULTIPLE_VALUES.indexOf(
                                                valKey
                                            ) > -1
                                        ) {
                                            entry.value[valKey] = entry.value[
                                                valKey
                                            ].split("\n");
                                        }
                                    });

                                    const values = Object.values(
                                        entry.value
                                    ).flat();
                                    if (values.includes("test")) {
                                        console.log("Word exists");
                                    }

                                    const createGraphDataFuseki = await readHkbdbData(
                                        Api.getSuggestionData(entry.graph)
                                    );

                                    const result = await doRestCreate(
                                        user,
                                        entry
                                    );

                                    await deleteOriginEvent(
                                        createGraphDataFuseki,
                                        key,
                                        entry
                                    );
                                    return result;
                                }
                            });
                        }
                        return [];
                    })
                    .flat()
            );

            // 在這裡處理所有結果
            const successfulResults = allResults.filter(
                result => !isEmpty(result)
            );

            if (successfulResults.length > 0) {
                const sendMail = {
                    from: emailConfig.daoyidhNoReply,
                    to: email,
                    cc: emailConfig.cuhk,
                    subject: "「香港作家及藝術家傳記資料庫」增補建議",
                    html: `
                <p>${displayName}：</p>
                <p>感謝閣下提供的建議，我們會盡快處理及回覆。</p>
                <br>
                <p>香港中文大學圖書館</p>
                `
                };
                await createEmail(sendMail);
            } else {
                // 處理全部失敗的情況
                console.log("All entries failed to create");
            }
            setIsCreatingSuggestions(false);
            setIsOpenHintModal(true);
            // 讓information重抓suggestions
            dispatch({
                type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                payload: {
                    target: null,
                    signal: `create-${new Date().getTime()}`
                }
            });

            setIsSaved(true);

            // 重置 tempUpdateSuggestInfo
            dispatch({
                type: Act.RESET_TEMP_UPDATE_SUGGESTINFO
            });
        } catch (error) {
            console.error("Error in handleCreate:", error);
            // setIsLoading(false);
        }
    };

    useEffect(() => {
        if (name === "") {
            return;
        }
        const send = async () => {
            // 頁面載入
            // setIsLoading(true); // 已處理，取消這邊的 loading 狀態
            await queryPersonInfo(name, dispatch);
            // 非同步獲取資料(先取得先顯示)，-1 抓取全部資料
            queryPersonInfoV2(name, null, -1, 0, null, dispatch);
            // 取得人物資訊
            await queryPersonNameNode(name, dispatch);
            // 取得人際關係的相反關係
            queryInverseRelationInfo(Api.getInverseRelation(), dispatch);
            // 取得 publication, article and otherWork needs list
            queryOntologyOneOfThemInfo(
                Api.getHkbdbOntologyOneOfThemInfo(),
                dispatch
            );
        };
        send();
    }, [name]);

    useEffect(() => {
        if (name === "") {
            return;
        }
        const send = async () => {
            queryGenealogy(1, name, dispatch);
            queryTimeLine(name, dispatch, setIsLoading); // put setIsLoading as args
        };
        send();
    }, [name, renderSignal, locale]);

    useEffect(() => {
        // const areAllArraysEmpty = obj => {
        //     if (typeof obj !== "object" || obj === null) {
        //         return false;
        //     }
        //     return Object.values(obj).every(
        //         value => Array.isArray(value) && value.length === 0
        //     );
        // };

        // 沒新增過suggestions，則不須跳出警示訊息
        // const isEmptySuggestions = areAllArraysEmpty(tempUpdateSuggestInfo);
        // if (isEmptySuggestions) return;

        const handleBeforeUnload = e => {
            if (!isSaved) {
                e.preventDefault();
                e.returnValue = "";
            }
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
        };
    }, [isSaved, tempUpdateSuggestInfo]);

    const createPane = (tabName, Component, accessible = false) => {
        // console.log("I am createPane");
        return {
            menuItem: tabName,
            render: () => (
                <Tab.Pane
                    style={{
                        minHeight: "550px",
                        paddingBottom: "100px",
                        overflow: "auto"
                    }}
                >
                    {user.role === "suggester" &&
                        tabName === formatMessage(intlMsgs["information"]) && (
                            <div style={{ textAlign: "right" }}>
                                <Button
                                    content={
                                        isCreatingSuggestions
                                            ? "提交中..."
                                            : "提交"
                                    }
                                    color="orange"
                                    onClick={handleCreate}
                                />
                            </div>
                        )}
                    {user.role === "suggester" &&
                        tabName === formatMessage(intlMsgs["information"]) && (
                            <SugSubmitHintModal
                                isOpen={isOpenHintModal}
                                setIsOpen={setIsOpenHintModal}
                            />
                        )}
                    {/* <WarningModal /> */}
                    {accessible && (
                        <Component
                            intl={intl}
                            type="person"
                            name={name}
                            className={className}
                            treeData={state.personInformation.familyTreeData}
                            permission={permission}
                            ReducerContext={StoreContext}
                        />
                    )}

                    {/* 該區塊暫不開放(僅適用於開發狀態) */}
                    {!accessible && (
                        <Segment style={{ padding: "4em 0em" }} vertical>
                            <Container textAlign="justified">
                                <UnderConstruction
                                    title={tabName}
                                    cardHeader={"資料準備中"}
                                    cardDescription={""}
                                />
                            </Container>
                        </Segment>
                    )}
                </Tab.Pane>
            )
        };
    };

    const panes = [
        createPane(formatMessage(intlMsgs["information"]), Information, true),
        createPane(formatMessage(intlMsgs["timeline"]), Timeline, true),
        createPane(formatMessage(intlMsgs["SNA"]), SNA, true),
        createPane(formatMessage(intlMsgs["familyTree"]), Tree, true)
    ];
    return isLoading ? (
        <MainLoader />
    ) : (
        <Tab panes={panes} style={{ minHeight: "75vh" }} />
    );
};
PeoplePageContent.propTypes = {
    intl: PropTypes.objectOf(PropTypes.any).isRequired,
    name: PropTypes.string.isRequired,
    permission: PropTypes.number,
    className: PropTypes.string
};

export default injectIntl(PeoplePageContent);
