import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject
import com.kms.katalon.core.checkpoint.Checkpoint as Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling as FailureHandling
import com.kms.katalon.core.testcase.TestCase as TestCase
import com.kms.katalon.core.testdata.TestData as TestData
import com.kms.katalon.core.testng.keyword.TestNGBuiltinKeywords as TestNGKW
import com.kms.katalon.core.testobject.TestObject as TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows
import internal.GlobalVariable as GlobalVariable
import org.openqa.selenium.Keys as Keys

WebUI.openBrowser('')

WebUI.navigateToUrl('https://hkbdb2.daoyidh.com/zh-hans/map')

WebUI.click(findTestObject('Object Repository/Page_HKBDB/p_'))

WebUI.click(findTestObject('Object Repository/Page_HKBDB/p__1'))

WebUI.click(findTestObject('Object Repository/Page_HKBDB/p__1_2'))

WebUI.click(findTestObject('Object Repository/Page_HKBDB/span_65'))

WebUI.click(findTestObject('Object Repository/Page_HKBDB/span_64'))

WebUI.click(findTestObject('Object Repository/Page_HKBDB/div_63            ()'))

WebUI.click(findTestObject('Object Repository/Page_HKBDB/canvas__leaflet-zoom-animated leaflet-interactive'))

WebUI.dragAndDropByOffset(findTestObject('Page_HKBDB/start-year-bar'), 200, 0)

WebUI.dragAndDropByOffset(findTestObject('Page_HKBDB/end-year-bar'), -200, 0)

WebUI.closeBrowser()

