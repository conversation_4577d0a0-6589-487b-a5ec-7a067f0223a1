/* eslint-disable no-unused-vars, react/prop-types */
import React from "react";
// leaflet
import { <PERSON><PERSON>, <PERSON>up, Tooltip } from "react-leaflet";
import classNames from "classnames";
// eslint-disable-next-line import/no-extraneous-dependencies
import isFunction from "lodash/isFunction";
import L from "leaflet";
// import { type ICluster } from "@/app/[lang]/activity/replus2023/subComponent/TypeScriptProps.tsx";
import {
    dfPointMarkerPalette
    // type PaletteKey,
} from "../../TraceMap/config.js";

// marker icon background color generator
const markerIconColor = (count, paletteStyle) => {
    const pointPalette =
        paletteStyle && paletteStyle in dfPointMarkerPalette
            ? dfPointMarkerPalette[paletteStyle]
            : dfPointMarkerPalette.light;
    const colorFound = pointPalette.find(
        pp => count >= pp.min && count <= pp.max
    );
    if (colorFound != null) {
        return colorFound.color;
    }
    return pointPalette[0].color;
};

const MAX_WIDTH = 40;
const safeWidth = (width, maxWidth) => (width > maxWidth ? maxWidth : width);

// generate point icon
const pointIcons = {};

// interface FetchPointIconProps {
//     count: number;
//     size: number;
//     locName: string;
//     paletteStyle?: PaletteKey;
//     className: string;
// }

const fetchPointIcon = ({ count, size, locName, paletteStyle, className }) => {
    const limitWidth = safeWidth(size, MAX_WIDTH);
    const color = markerIconColor(count, paletteStyle);

    const str = locName === "" ? `${count}` : `<br/>${count}<br/>${locName}`;
    const iconHtml = `
<div class="point-marker-icon-wrapper" style="position: relative; left: -${limitWidth /
        2}px; top: -${limitWidth / 2}px">
	<div class="${classNames(
        "point-marker-icon",
        className
    )}" style="width: ${limitWidth}px;height: ${limitWidth}px; text-align: center; background: ${color}">
		${str}
	</div>
</div>`;
    pointIcons[count] = L.divIcon({
        html: iconHtml
    });
    return pointIcons[count];
};

// interface PointMarkerProps {
//     clusterIndex: string | number;
//     cluster: ICluster;
//     latitude: number;
//     longitude: number;
//     clusterPoints: any[];
//     isLocNameDisplay?: boolean;
//     paletteStyle?: PaletteKey;
//     onPopupOpen: (cluster: ICluster) => void;
//     onClick: (cluster: ICluster) => void;
//     onTooltipOpen: (cluster: ICluster) => void;
//     showPopup: boolean;
//     showTooltip: boolean;
//     pointIconClassName: string;
// }

// interface PopupTextProps {
//     location?: string;
//     intensity?: number;
// }

const PopupText = ({ location, intensity }) => (
    <>
        {location ?? "Unknown"}({intensity})
    </>
);

const PointMarker = ({
    clusterIndex,
    cluster,
    latitude,
    longitude,
    clusterPoints,
    isLocNameDisplay,
    paletteStyle,
    onPopupOpen,
    onClick,
    onTooltipOpen,
    showPopup,
    showTooltip,
    pointIconClassName
}) => {
    const { location, intensity } = cluster.properties;

    // 若定義 Marker 的 click event, 則 Popup 的 onOpen event 則會失效

    return (
        <Marker
            /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
            // @ts-expect-error
            className="pointMarker"
            key={`cluster-${clusterIndex}-${cluster.properties.locId}`}
            position={L.latLng({ lat: latitude, lng: longitude })}
            icon={fetchPointIcon({
                count: cluster?.properties?.intensity,
                size:
                    10 +
                    (cluster.properties.intensity / clusterPoints.length) * 130,
                locName:
                    isLocNameDisplay && cluster?.properties?.location
                        ? cluster.properties.location
                        : "",
                paletteStyle,
                className: pointIconClassName
            })}
            eventHandlers={{
                click: e => {
                    if (isFunction(onClick)) {
                        onClick(cluster);
                    }
                }
            }}
        >
            {showPopup && (
                <Popup
                    /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
                    // @ts-expect-error
                    onOpen={() => {
                        if (isFunction(onPopupOpen)) {
                            onPopupOpen(cluster);
                        }
                    }}
                >
                    <PopupText location={location} intensity={intensity} />
                </Popup>
            )}
            {showTooltip && (
                <Tooltip
                    className="marker--tooltip"
                    /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
                    // @ts-expect-error
                    onOpen={() => {
                        if (isFunction(onTooltipOpen)) {
                            onTooltipOpen(cluster);
                        }
                    }}
                >
                    <span
                        style={{
                            fontWeight: "bold",
                            maxWidth: "50px",
                            whiteSpace: "pre"
                        }}
                    >
                        <PopupText />
                    </span>
                </Tooltip>
            )}
        </Marker>
    );
};

export default PointMarker;
