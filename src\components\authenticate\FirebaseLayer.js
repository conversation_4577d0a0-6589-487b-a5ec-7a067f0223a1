// firebase
import firebase from "firebase/app";
import "firebase/auth";
import "firebase/database";
import "firebase/firestore";

import AuthListener from "./firebase/AuthListener";
import RealTimeListener from "./firebase/RealTimeListener";
import StorageListener from "./firebase/StorageListener";
import FirestoreListener from "./firebase/FirestoreListener";
import firebaseConfig from "../../config/config-firebase";

firebase.initializeApp(firebaseConfig);

const realTimeDb = firebase.database();
const firestoreDb = firebase.firestore();
const firebaseAuth = firebase.auth();

const FirebaseLayer = props => {
    AuthListener({ firebaseAuth });
    RealTimeListener({ firebaseAuth, realTimeDb });
    FirestoreListener({ firestoreDb });
    StorageListener();

    return props.children || null;
};

export default FirebaseLayer;
